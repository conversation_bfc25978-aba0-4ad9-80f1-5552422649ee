# 🚀 Cloudflare Workers 签到兑换码系统 - 完整部署方案

## 📋 项目概述

一个基于 Cloudflare Workers + D1 + Linux.Do OAuth2 的签到兑换码系统，单文件部署，配置简单。支持管理员灵活设置奖励机制和统一发放功能。

## 🔧 部署步骤

### 1. 创建 Cloudflare 资源
```bash
# 创建 D1 数据库
wrangler d1 create signin-exchange-db

# 创建 KV 命名空间
wrangler kv:namespace create SESSION_STORE
```

### 2. 配置 wrangler.toml
```toml
name = "signin-exchange-system"
main = "worker.js"
compatibility_date = "2024-01-01"

[[d1_databases]]
binding = "DB"
database_name = "signin-exchange-db"
database_id = "your-d1-database-id"

[[kv_namespaces]]
binding = "KV"
id = "your-kv-namespace-id"
```

### 3. 设置环境变量
在 Cloudflare Dashboard 的 Workers 设置中添加：

```javascript
// Environment Variables
LINUX_DO_CLIENT_ID = "your_linux_do_client_id"
LINUX_DO_CLIENT_SECRET = "your_linux_do_client_secret" 
REDIRECT_URI = "https://your-worker.your-subdomain.workers.dev/auth/callback"
JWT_SECRET = "your_random_jwt_secret_32_chars_long"

// 管理员登录配置（用户名和密码方式）
ADMIN_USERNAME = "admin"                    // 管理员用户名
ADMIN_PASSWORD = "your_secure_password"     // 管理员密码
```

### 4. Linux.Do OAuth2 应用配置
访问 [Connect.Linux.Do](https://connect.linux.do) 创建应用：
- 应用名称：签到兑换码系统
- 回调地址：`https://your-worker.your-subdomain.workers.dev/auth/callback`
- 获取 Client ID 和 Client Secret

### 5. 部署 worker.js
```bash
wrangler deploy
```

## 📁 完整文件结构
```
project/
├── worker.js          # 主程序文件（唯一需要的代码文件）
├── wrangler.toml      # Cloudflare 配置
└── README.md          # 说明文档
```

## 🎯 核心功能

### 用户功能
- ✅ Linux.Do OAuth2 一键登录
- ✅ 主界面显示签到统计（连续签到天数、总签到天数）
- ✅ 每日签到获取兑换码（UTC+8时区）
- ✅ 弹窗显示获得的兑换码（可复制/关闭）
- ✅ 连续签到额外奖励机制
- ✅ 个人数据统计（专有ID、总额度显示）
- ✅ 兑换码记录查看（分页显示，5条/页）
- ✅ 用户信息展示（头像、昵称、信任等级）

### 管理功能
- ✅ 用户名密码登录管理后台
- ✅ TXT文件导入兑换码（指定统一金额）
- ✅ **统一发放兑换码**（一键给所有用户发放）
- ✅ **指定用户发放**（选择用户批量发放）
- ✅ **签到排名发放**（签到前N名奖励功能）
- ✅ 用户管理（头像、昵称、等级、LinuxDO ID、总额度）
- ✅ 用户搜索（昵称、ID模糊查询）
- ✅ 签到奖励配置（基础奖励、连续奖励可修改）
- ✅ **分页管理**（5-50条可调节，支持10,15,20,30,50）
- ✅ 系统统计数据和操作日志

### UI设计
- 🎨 主题色：RGB(234,245,246)
- 🌙 深色模式适配
- 📱 响应式设计
- ⚠️ 防重复弹窗机制

## 🗄️ 数据库结构

### 自动建表SQL
```sql
-- 用户信息表
CREATE TABLE oauth_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    site_user_id VARCHAR(20) UNIQUE,        -- 网站专有ID（6位数字）
    linux_do_id INTEGER UNIQUE NOT NULL, 
    username VARCHAR(50) NOT NULL,        
    name VARCHAR(100),                    
    avatar_url TEXT,                      
    trust_level INTEGER DEFAULT 0,       
    total_signin_days INTEGER DEFAULT 0,  
    continuous_signin_days INTEGER DEFAULT 0,
    total_exchange_amount DECIMAL(10,2) DEFAULT 0.00,
    last_signin_date VARCHAR(10),           -- UTC+8 日期 YYYY-MM-DD
    created_at DATETIME,
    updated_at DATETIME
);

-- 签到记录表
CREATE TABLE signin_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    site_user_id VARCHAR(20),
    signin_datetime VARCHAR(19),            -- UTC+8 时间 YYYY-MM-DD HH:mm:ss
    reward_amount DECIMAL(8,2),
    continuous_days INTEGER,
    bonus_amount DECIMAL(8,2) DEFAULT 0.00, -- 连续签到奖励
    ip_address VARCHAR(50),
    created_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES oauth_users (id)
);

-- 兑换码表
CREATE TABLE exchange_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(100) UNIQUE NOT NULL,
    amount DECIMAL(8,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'available', -- available, used
    user_id INTEGER,
    site_user_id VARCHAR(20),
    exchange_type VARCHAR(20),              -- signin, reward, admin_distribute
    issued_datetime VARCHAR(19),            -- UTC+8 时间
    admin_user_id INTEGER,
    created_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES oauth_users (id)
);

-- 系统配置表
CREATE TABLE system_configs (
    config_key VARCHAR(50) PRIMARY KEY,
    config_value TEXT,
    description TEXT,
    updated_at DATETIME
);

-- 操作日志表
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    admin_user_id INTEGER,
    operation_type VARCHAR(50),
    operation_desc TEXT,
    ip_address VARCHAR(50),
    status VARCHAR(20),
    created_at DATETIME
);

-- 弹窗记录表（防重复弹窗）
CREATE TABLE popup_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    site_user_id VARCHAR(20),
    popup_type VARCHAR(20),                 -- signin, reward
    exchange_code_ids TEXT,                 -- JSON数组
    is_closed BOOLEAN DEFAULT FALSE,
    created_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES oauth_users (id)
);

-- 管理员会话表
CREATE TABLE admin_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_token VARCHAR(128) UNIQUE,
    username VARCHAR(50),
    ip_address VARCHAR(50),
    expires_at DATETIME,
    created_at DATETIME
);
```

### 初始系统配置
```sql
INSERT INTO system_configs VALUES 
('base_signin_reward', '1.00', '基础签到奖励金额（美元）', datetime('now')),
('continuous_5_bonus', '0.50', '连续5天签到额外奖励（美元）', datetime('now')),
('continuous_10_bonus', '5.00', '连续10天签到额外奖励（美元）', datetime('now')),
('signin_ranking_count', '10', '签到排名奖励人数', datetime('now')),
('ranking_bonus_amount', '2.00', '签到排名奖励金额（美元）', datetime('now')),
('default_page_size', '20', '默认分页大小', datetime('now'));
```

## 🌐 访问地址

部署完成后访问：
- **用户页面**: `https://your-worker.your-subdomain.workers.dev/`
- **管理后台**: `https://your-worker.your-subdomain.workers.dev/admin`
- **管理员登录**: `https://your-worker.your-subdomain.workers.dev/admin/login`
- **API接口**: `https://your-worker.your-subdomain.workers.dev/api/*`

## 🔑 完整API路由

### 用户端API
```
GET  /                     # 用户主页（显示签到统计）
GET  /auth/oauth-url      # 获取授权链接
GET  /auth/callback       # OAuth2回调
POST /auth/logout         # 用户登出

GET  /api/user/profile    # 获取用户信息（含签到统计）
POST /api/user/signin     # 用户签到
GET  /api/user/codes      # 我的兑换码（分页）
GET  /api/user/popup      # 获取未弹窗的兑换码
POST /api/user/popup/close # 关闭弹窗
```

### 管理员API
```
GET  /admin/login                  # 管理员登录页面
POST /admin/login                  # 管理员登录处理
POST /admin/logout                 # 管理员登出
GET  /admin                        # 管理后台首页

GET  /api/admin/stats              # 系统统计数据
GET  /api/admin/users              # 用户列表（支持搜索、分页）
GET  /api/admin/codes              # 兑换码列表（分页）
GET  /api/admin/configs            # 系统配置
GET  /api/admin/signin-records     # 签到记录（分页）

POST /api/admin/codes/upload       # TXT文件批量导入兑换码
POST /api/admin/codes/distribute   # 统一发放兑换码
POST /api/admin/codes/assign       # 指定用户发放
POST /api/admin/signin/ranking     # 签到排名发放

PUT  /api/admin/configs            # 更新系统配置
GET  /api/admin/logs               # 操作日志（分页）
POST /api/admin/export/users       # 导出用户数据
POST /api/admin/export/records     # 导出兑换记录
```

## 👤 用户主界面设计

### 1. 用户主页布局
```html
<!-- 用户主界面 -->
<div class="user-dashboard">
  <!-- 用户信息卡片 -->
  <div class="user-profile-card">
    <div class="user-avatar">
      <img src="avatar_url" alt="用户头像">
    </div>
    <div class="user-info">
      <h3>用户昵称</h3>
      <p>专有ID: #123456</p>
      <p>LinuxDO ID: 789012</p>
      <p>信任等级: Level 2</p>
      <p class="total-amount">总额度: $25.50</p>
    </div>
  </div>

  <!-- 签到统计卡片 -->
  <div class="signin-stats-card">
    <h3>📊 签到统计</h3>
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">连续签到</span>
        <span class="stat-value">{continuous_signin_days} 天</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">累计签到</span>
        <span class="stat-value">{total_signin_days} 天</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日状态</span>
        <span class="stat-value">{signin_status}</span>
      </div>
    </div>
  </div>

  <!-- 签到按钮 -->
  <div class="signin-section">
    <button id="signinBtn" class="signin-btn" onclick="dailySignin()">
      🎯 每日签到
    </button>
    <p class="signin-hint">
      下次可签到时间: {next_signin_time}
    </p>
  </div>

  <!-- 快捷操作 -->
  <div class="quick-actions">
    <button onclick="showMyCodes()" class="action-btn">
      📋 我的兑换码
    </button>
    <button onclick="showProfile()" class="action-btn">
      👤 个人资料
    </button>
  </div>
</div>
```

### 2. 签到统计API响应
```javascript
// GET /api/user/profile 响应示例
{
  "success": true,
  "user": {
    "site_user_id": "123456",
    "name": "用户昵称",
    "avatar_url": "https://...",
    "trust_level": 2,
    "linux_do_id": 789012,
    "total_exchange_amount": 25.50,
    // 签到统计信息
    "signin_stats": {
      "continuous_signin_days": 7,        // 连续签到天数
      "total_signin_days": 15,            // 总签到天数
      "today_signed": true,               // 今日是否已签到
      "last_signin_date": "2024-01-15",   // 最后签到日期
      "next_signin_time": "2024-01-16 00:00:00", // 下次可签到时间
      "signin_streak_reward": 1.50        // 连续签到奖励预览
    }
  }
}
```

## 🔐 管理员登录系统

### 1. 管理员登录界面
```html
<!-- 管理员登录页面 /admin/login -->
<div class="admin-login-container">
  <div class="login-form">
    <h2>🔐 管理员登录</h2>
    <form onsubmit="adminLogin(event)">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" name="username" required>
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" name="password" required>
      </div>
      <button type="submit" class="login-btn">登录</button>
    </form>
  </div>
</div>
```

### 2. 管理员会话管理
```javascript
// 管理员登录处理
class AdminAuth {
  static async login(username, password, ipAddress) {
    // 验证用户名密码
    const adminUsername = env.ADMIN_USERNAME;
    const adminPassword = env.ADMIN_PASSWORD;
  
    if (username !== adminUsername || password !== adminPassword) {
      throw new Error('用户名或密码错误');
    }
  
    // 生成会话token
    const sessionToken = this.generateSessionToken();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时
  
    // 保存会话
    await env.DB.prepare(`
      INSERT INTO admin_sessions (session_token, username, ip_address, expires_at, created_at)
      VALUES (?, ?, ?, ?, datetime('now'))
    `).bind(sessionToken, username, ipAddress, expiresAt.toISOString()).run();
  
    return sessionToken;
  }

  static async validateSession(sessionToken) {
    const session = await env.DB.prepare(`
      SELECT * FROM admin_sessions 
      WHERE session_token = ? AND expires_at > datetime('now')
    `).bind(sessionToken).first();
  
    return session;
  }

  static generateSessionToken() {
    return crypto.randomUUID().replace(/-/g, '') + Date.now().toString(36);
  }
}
```

## 📊 管理员分页系统

### 1. 分页组件设计
```html
<!-- 分页控制组件 -->
<div class="pagination-controls">
  <div class="page-size-selector">
    <label>每页显示:</label>
    <select onchange="changePageSize(this.value)">
      <option value="5">5条</option>
      <option value="10">10条</option>
      <option value="15">15条</option>
      <option value="20" selected>20条</option>
      <option value="30">30条</option>
      <option value="50">50条</option>
    </select>
  </div>

  <div class="pagination-info">
    显示第 <span id="startRecord">1</span>-<span id="endRecord">20</span> 条
    / 共 <span id="totalRecords">156</span> 条记录
  </div>

  <div class="pagination-buttons">
    <button onclick="goToPage(1)" class="page-btn">首页</button>
    <button onclick="goToPage(currentPage - 1)" class="page-btn">上一页</button>
  
    <div class="page-numbers">
      <!-- 动态生成页码按钮 -->
    </div>
  
    <button onclick="goToPage(currentPage + 1)" class="page-btn">下一页</button>
    <button onclick="goToPage(totalPages)" class="page-btn">末页</button>
  </div>
</div>
```

### 2. 分页API实现
```javascript
// 统一分页参数处理
class PaginationHelper {
  static getParams(url) {
    const params = new URL(url).searchParams;
    const page = Math.max(1, parseInt(params.get('page')) || 1);
    const limit = this.validateLimit(parseInt(params.get('limit')) || 20);
    const offset = (page - 1) * limit;
  
    return { page, limit, offset };
  }

  static validateLimit(limit) {
    const allowedSizes = [5, 10, 15, 20, 30, 50];
    return allowedSizes.includes(limit) ? limit : 20;
  }

  static buildResponse(data, total, page, limit) {
    const totalPages = Math.ceil(total / limit);
  
    return {
      success: true,
      data: data,
      pagination: {
        page: page,
        limit: limit,
        total: total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        startRecord: (page - 1) * limit + 1,
        endRecord: Math.min(page * limit, total)
      }
    };
  }
}

// 用户列表API（带分页）
async function getUsersList(request) {
  const { page, limit, offset } = PaginationHelper.getParams(request.url);
  const search = new URL(request.url).searchParams.get('search') || '';

  // 构建查询条件
  let whereClause = '1=1';
  let params = [];

  if (search) {
    whereClause += ` AND (name LIKE ? OR username LIKE ? OR site_user_id LIKE ? OR linux_do_id LIKE ?)`;
    const searchPattern = `%${search}%`;
    params.push(searchPattern, searchPattern, searchPattern, searchPattern);
  }

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM oauth_users WHERE ${whereClause}`;
  const { total } = await env.DB.prepare(countQuery).bind(...params).first();

  // 获取分页数据
  const dataQuery = `
    SELECT site_user_id, linux_do_id, username, name, avatar_url, 
           trust_level, total_signin_days, continuous_signin_days, 
           total_exchange_amount, created_at
    FROM oauth_users 
    WHERE ${whereClause}
    ORDER BY created_at DESC 
    LIMIT ? OFFSET ?
  `;

  const users = await env.DB.prepare(dataQuery)
    .bind(...params, limit, offset)
    .all();

  return PaginationHelper.buildResponse(users.results, total, page, limit);
}
```

### 3. 管理员界面列表组件
```html
<!-- 用户管理列表 -->
<div class="admin-users-section">
  <div class="section-header">
    <h3>👥 用户管理</h3>
    <div class="search-container">
      <input type="text" placeholder="搜索用户昵称、ID..." id="userSearch">
      <button onclick="searchUsers()">搜索</button>
      <button onclick="clearSearch()">清除</button>
    </div>
  </div>

  <!-- 批量操作 -->
  <div class="batch-actions">
    <button onclick="selectAllUsers()" class="btn-secondary">全选</button>
    <button onclick="batchDistribute()" class="btn-primary">批量发放</button>
    <span class="selected-count">已选择: <span id="selectedCount">0</span> 人</span>
  </div>

  <!-- 用户列表 -->
  <div class="users-table">
    <table>
      <thead>
        <tr>
          <th><input type="checkbox" id="selectAll"></th>
          <th>头像</th>
          <th>昵称</th>
          <th>专有ID</th>
          <th>LinuxDO ID</th>
          <th>等级</th>
          <th>总额度</th>
          <th>签到天数</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody id="usersTableBody">
        <!-- 动态生成用户行 -->
      </tbody>
    </table>
  </div>

  <!-- 分页控件 -->
  <div class="pagination-controls">
    <!-- 分页组件 -->
  </div>
</div>

<!-- 兑换码管理列表 -->
<div class="admin-codes-section">
  <div class="section-header">
    <h3>💳 兑换码管理</h3>
    <div class="filter-controls">
      <select id="statusFilter">
        <option value="">全部状态</option>
        <option value="available">可用</option>
        <option value="used">已使用</option>
      </select>
      <select id="typeFilter">
        <option value="">全部类型</option>
        <option value="signin">签到</option>
        <option value="reward">奖励</option>
        <option value="admin_distribute">管理员发放</option>
      </select>
    </div>
  </div>

  <div class="codes-table">
    <table>
      <thead>
        <tr>
          <th>兑换码</th>
          <th>金额</th>
          <th>状态</th>
          <th>类型</th>
          <th>用户</th>
          <th>发放时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody id="codesTableBody">
        <!-- 动态生成兑换码行 -->
      </tbody>
    </table>
  </div>

  <!-- 分页控件 -->
  <div class="pagination-controls">
    <!-- 分页组件 -->
  </div>
</div>
```

### 4. 分页JavaScript处理
```javascript
// 分页管理器
class AdminPagination {
  constructor(endpoint, containerId, renderCallback) {
    this.endpoint = endpoint;
    this.containerId = containerId;
    this.renderCallback = renderCallback;
    this.currentPage = 1;
    this.pageSize = 20;
    this.total = 0;
    this.filters = {};
  }

  async loadData() {
    const params = new URLSearchParams({
      page: this.currentPage,
      limit: this.pageSize,
      ...this.filters
    });
  
    try {
      const response = await fetch(`${this.endpoint}?${params}`);
      const result = await response.json();
    
      if (result.success) {
        this.total = result.pagination.total;
        this.renderCallback(result.data);
        this.updatePaginationUI(result.pagination);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  }

  changePage(page) {
    this.currentPage = page;
    this.loadData();
  }

  changePageSize(size) {
    this.pageSize = size;
    this.currentPage = 1;
    this.loadData();
  }

  setFilters(filters) {
    this.filters = { ...filters };
    this.currentPage = 1;
    this.loadData();
  }

  updatePaginationUI(pagination) {
    // 更新分页信息显示
    document.getElementById('startRecord').textContent = pagination.startRecord;
    document.getElementById('endRecord').textContent = pagination.endRecord;
    document.getElementById('totalRecords').textContent = pagination.total;
  
    // 更新页码按钮
    this.renderPageNumbers(pagination);
  }

  renderPageNumbers(pagination) {
    const container = document.querySelector('.page-numbers');
    const { page, totalPages } = pagination;
    let html = '';
  
    // 计算显示的页码范围
    let start = Math.max(1, page - 2);
    let end = Math.min(totalPages, page + 2);
  
    for (let i = start; i <= end; i++) {
      html += `
        <button class="page-btn ${i === page ? 'active' : ''}" 
                onclick="pagination.changePage(${i})">
          ${i}
        </button>
      `;
    }
  
    container.innerHTML = html;
  }
}

// 初始化各个分页实例
const usersPagination = new AdminPagination('/api/admin/users', 'usersTableBody', renderUsersTable);
const codesPagination = new AdminPagination('/api/admin/codes', 'codesTableBody', renderCodesTable);
const logsPagination = new AdminPagination('/api/admin/logs', 'logsTableBody', renderLogsTable);
```

## ⚙️ 签到奖励机制

### 1. 基础奖励（管理员可配置）
```javascript
// 默认配置
基础签到奖励: $1.00         // base_signin_reward
连续5天额外奖励: $0.50      // continuous_5_bonus
连续10天额外奖励: $5.00     // continuous_10_bonus
```

### 2. 奖励计算逻辑
```javascript
签到奖励计算:
- 基础奖励: 管理员设置的基础金额
- 连续5天: 基础奖励 + 连续5天奖励
- 连续10天: 基础奖励 + 连续5天奖励 + 连续10天奖励
- 中断后重新开始计算
```

### 3. 签到排名奖励
```javascript
// 管理员可在签到记录中选择前N名用户进行额外发放
排名奖励机制:
- 每日签到时间排序
- 管理员设置奖励人数（默认前10名）
- 管理员设置奖励金额（默认$2.00）
- 支持批量选择发放
```

## 🎨 UI界面设计

### 1. 主题配色方案
```css
:root {
  --primary-color: rgb(234, 245, 246);      /* 主题色 */
  --primary-dark: rgb(200, 230, 232);       /* 主题色深色 */
  --bg-dark: #1a1a1a;                       /* 深色背景 */
  --text-light: #ffffff;                    /* 深色模式文字 */
  --text-dark: #333333;                     /* 浅色模式文字 */
  --border-color: rgba(234, 245, 246, 0.3); /* 边框色 */
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: var(--bg-dark);
    color: var(--text-light);
  }

  .card {
    background-color: rgba(234, 245, 246, 0.1);
    border: 1px solid var(--border-color);
  }

  input, select, textarea {
    background-color: rgba(234, 245, 246, 0.05);
    border: 1px solid var(--border-color);
    color: var(--text-light);
  }

  table {
    background-color: rgba(234, 245, 246, 0.05);
  }

  th, td {
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
  }
}
```

## 🔒 防重复弹窗机制

### 1. 弹窗控制逻辑
````javascript
// 弹窗状态管理
class PopupManager {
  static async showExchangeCode(userId, codes) {
    // 检查是否有未关闭的弹窗
    const existingPopup = await this.getUnClosedPopup(userId);
    if (existingPopup && !existingPopup.is_closed) {
      return false; // 阻止重复弹窗
    }
  


javascript
    // 创建新弹窗记录
    await this.createPopupRecord(userId, codes);
  
    // 显示弹窗（只显示最新的兑换码）
    const latestCode = codes[codes.length - 1];
    this.displayPopup(latestCode);
  
    return true;
  }

  static async closePopup(userId) {
    // 标记弹窗已关闭
    await this.markPopupClosed(userId);
  }
}


### 2. 兑换码分页显示
javascript
// 我的兑换码页面
async function loadExchangeCodes(page = 1) {
  const response = await fetch(`/api/user/codes?page=${page}&limit=5`);
  const data = await response.json();

  displayExchangeCodes(data.codes);
  updatePagination(data.pagination);
}

function displayExchangeCodes(codes) {
  const container = document.getElementById('codesContainer');
  container.innerHTML = codes.map(code => `
    <div class="code-item">
      <div class="code-info">
        <span class="code">${code.code}</span>
        <span class="amount">$${code.amount}</span>
        <span class="type">${code.exchange_type === 'signin' ? '签到' : '奖励'}</span>
        <span class="date">${code.issued_datetime}</span>
      </div>
      <button onclick="copyToClipboard('${code.code}')" class="btn-copy">复制</button>
    </div>
  `).join('');
}
```

## 📊 管理员功能详解

### 1. TXT文件导入兑换码
```javascript
// 管理员上传界面
<form enctype="multipart/form-data">
  <input type="file" accept=".txt" id="codesFile">
  <input type="number" step="0.01" placeholder="统一金额（美元）" id="codeAmount">
  <button onclick="uploadCodes()">导入兑换码</button>
</form>

// 上传处理
async function uploadCodes() {
  const file = document.getElementById('codesFile').files[0];
  const amount = document.getElementById('codeAmount').value;

  const formData = new FormData();
  formData.append('file', file);
  formData.append('amount', amount);

  const response = await fetch('/api/admin/codes/upload', {
    method: 'POST',
    body: formData
  });
}
```

### 2. 系统配置管理
```javascript
// 配置项界面
<div class="config-section">
  <h3>签到奖励配置</h3>
  <div class="config-item">
    <label>基础签到奖励（美元）:</label>
    <input type="number" step="0.01" id="baseReward">
  </div>
  <div class="config-item">
    <label>连续5天额外奖励（美元）:</label>
    <input type="number" step="0.01" id="continuous5Bonus">
  </div>
  <div class="config-item">
    <label>连续10天额外奖励（美元）:</label>
    <input type="number" step="0.01" id="continuous10Bonus">
  </div>
  <button onclick="saveConfigs()">保存配置</button>
</div>
```

### 3. 高级分页功能
```javascript
// 管理员分页配置
class AdminTableManager {
  constructor(options) {
    this.endpoint = options.endpoint;
    this.containerId = options.containerId;
    this.renderCallback = options.renderCallback;
    this.currentPage = 1;
    this.pageSize = parseInt(localStorage.getItem(`pageSize_${options.name}`)) || 20;
    this.filters = {};
    this.sortBy = options.defaultSort || 'created_at';
    this.sortOrder = 'DESC';
  }

  // 保存分页设置到本地存储
  savePageSize(size) {
    this.pageSize = size;
    localStorage.setItem(`pageSize_${this.name}`, size);
    this.currentPage = 1;
    this.loadData();
  }

  // 添加排序功能
  sort(column) {
    if (this.sortBy === column) {
      this.sortOrder = this.sortOrder === 'ASC' ? 'DESC' : 'ASC';
    } else {
      this.sortBy = column;
      this.sortOrder = 'ASC';
    }
    this.loadData();
  }

  // 导出当前筛选的数据
  async exportData(format = 'csv') {
    const params = new URLSearchParams({
      ...this.filters,
      sort: this.sortBy,
      order: this.sortOrder,
      export: format
    });
  
    const response = await fetch(`${this.endpoint}/export?${params}`);
    const blob = await response.blob();
  
    // 下载文件
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `export_${Date.now()}.${format}`;
    a.click();
  }
}
```

### 4. 增强的用户搜索功能
```html
<!-- 高级搜索界面 -->
<div class="advanced-search">
  <div class="search-row">
    <input type="text" placeholder="搜索用户昵称、ID..." id="userSearch">
    <select id="trustLevelFilter">
      <option value="">所有等级</option>
      <option value="0">新手用户</option>
      <option value="1">基础用户</option>
      <option value="2">会员</option>
      <option value="3">常客</option>
      <option value="4">领导者</option>
    </select>
  </div>

  <div class="search-row">
    <input type="date" id="registerDateFrom" placeholder="注册开始日期">
    <input type="date" id="registerDateTo" placeholder="注册结束日期">
    <select id="signinDaysFilter">
      <option value="">签到天数</option>
      <option value="0">未签到</option>
      <option value="1-7">1-7天</option>
      <option value="8-30">8-30天</option>
      <option value="30+">30天以上</option>
    </select>
  </div>

  <div class="search-actions">
    <button onclick="applyAdvancedSearch()" class="btn-primary">搜索</button>
    <button onclick="resetSearch()" class="btn-secondary">重置</button>
    <button onclick="exportSearchResults()" class="btn-info">导出结果</button>
  </div>
</div>
```

### 5. 批量操作增强
```javascript
// 批量操作管理器
class BatchOperationManager {
  constructor() {
    this.selectedItems = new Set();
    this.maxBatchSize = 100; // 最大批量操作数量
  }

  // 选择项目
  selectItem(id) {
    if (this.selectedItems.size >= this.maxBatchSize) {
      alert(`最多只能选择${this.maxBatchSize}个项目`);
      return false;
    }
    this.selectedItems.add(id);
    this.updateUI();
    return true;
  }

  // 取消选择
  deselectItem(id) {
    this.selectedItems.delete(id);
    this.updateUI();
  }

  // 全选当前页
  selectCurrentPage(itemIds) {
    const availableSlots = this.maxBatchSize - this.selectedItems.size;
    const toSelect = itemIds.slice(0, availableSlots);
  
    toSelect.forEach(id => this.selectedItems.add(id));
    this.updateUI();
  
    if (itemIds.length > availableSlots) {
      alert(`由于数量限制，只选择了前${toSelect.length}个项目`);
    }
  }

  // 批量发放兑换码
  async batchDistribute() {
    if (this.selectedItems.size === 0) {
      alert('请先选择用户');
      return;
    }
  
    const amount = prompt('请输入发放金额（美元）:');
    if (!amount || isNaN(amount) || amount <= 0) {
      alert('请输入有效金额');
      return;
    }
  
    const confirmed = confirm(`确定向${this.selectedItems.size}个用户发放$${amount}的兑换码吗？`);
    if (!confirmed) return;
  
    try {
      const response = await fetch('/api/admin/codes/batch-distribute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_ids: Array.from(this.selectedItems),
          amount: parseFloat(amount)
        })
      });
    
      const result = await response.json();
      if (result.success) {
        alert(`成功发放${result.distributed_count}个兑换码`);
        this.clearSelection();
        // 刷新数据
        usersPagination.loadData();
      } else {
        alert(`发放失败: ${result.message}`);
      }
    } catch (error) {
      alert('发放失败，请稍后重试');
    }
  }

  // 更新UI显示
  updateUI() {
    document.getElementById('selectedCount').textContent = this.selectedItems.size;
  
    // 更新批量操作按钮状态
    const batchButtons = document.querySelectorAll('.batch-action-btn');
    batchButtons.forEach(btn => {
      btn.disabled = this.selectedItems.size === 0;
    });
  
    // 更新选择框状态
    document.querySelectorAll('input[type="checkbox"][data-item-id]').forEach(checkbox => {
      const itemId = checkbox.dataset.itemId;
      checkbox.checked = this.selectedItems.has(itemId);
    });
  }
}
```

### 6. 实时统计看板
```html
<!-- 管理员统计看板 -->
<div class="admin-dashboard">
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon">👥</div>
      <div class="stat-content">
        <h3 id="totalUsers">-</h3>
        <p>总用户数</p>
        <small class="stat-change">+12 今日新增</small>
      </div>
    </div>
  
    <div class="stat-card">
      <div class="stat-icon">✅</div>
      <div class="stat-content">
        <h3 id="todaySignins">-</h3>
        <p>今日签到</p>
        <small class="stat-change">85.6% 签到率</small>
      </div>
    </div>
  
    <div class="stat-card">
      <div class="stat-icon">💳</div>
      <div class="stat-content">
        <h3 id="availableCodes">-</h3>
        <p>可用兑换码</p>
        <small class="stat-change">$1,250 总价值</small>
      </div>
    </div>
  
    <div class="stat-card">
      <div class="stat-icon">💰</div>
      <div class="stat-content">
        <h3 id="monthlyDistributed">-</h3>
        <p>本月发放</p>
        <small class="stat-change">$850 较上月</small>
      </div>
    </div>
  </div>

  <!-- 实时活动日志 -->
  <div class="activity-feed">
    <h3>📊 实时活动</h3>
    <div class="activity-list" id="activityList">
      <!-- 实时更新的活动记录 -->
    </div>
  </div>
</div>
```

### 7. 高级数据导出功能
```javascript
// 数据导出管理器
class DataExportManager {
  static async exportUsers(options = {}) {
    const {
      format = 'csv',
      filters = {},
      includeStats = true,
      dateRange = null
    } = options;
  
    const params = new URLSearchParams({
      format,
      includeStats: includeStats.toString(),
      ...filters
    });
  
    if (dateRange) {
      params.append('startDate', dateRange.start);
      params.append('endDate', dateRange.end);
    }
  
    try {
      const response = await fetch(`/api/admin/export/users?${params}`);
    
      if (response.ok) {
        const blob = await response.blob();
        this.downloadFile(blob, `users_export_${Date.now()}.${format}`);
      } else {
        throw new Error('导出失败');
      }
    } catch (error) {
      alert('导出失败，请稍后重试');
    }
  }

  static async exportSigninRecords(options = {}) {
    const {
      format = 'csv',
      userId = null,
      dateRange = null,
      includeRewards = true
    } = options;
  
    const params = new URLSearchParams({
      format,
      includeRewards: includeRewards.toString()
    });
  
    if (userId) params.append('userId', userId);
    if (dateRange) {
      params.append('startDate', dateRange.start);
      params.append('endDate', dateRange.end);
    }
  
    try {
      const response = await fetch(`/api/admin/export/signin-records?${params}`);
    
      if (response.ok) {
        const blob = await response.blob();
        this.downloadFile(blob, `signin_records_${Date.now()}.${format}`);
      }
    } catch (error) {
      alert('导出失败，请稍后重试');
    }
  }

  static downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
}
```

## 💡 系统优化特性

### 1. 性能优化
```javascript
// 数据库查询优化
class DatabaseOptimizer {
  // 创建索引
  static async createIndexes() {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_linux_do_id ON oauth_users(linux_do_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_site_id ON oauth_users(site_user_id)',
      'CREATE INDEX IF NOT EXISTS idx_signin_user_date ON signin_records(user_id, signin_datetime)',
      'CREATE INDEX IF NOT EXISTS idx_codes_user_status ON exchange_codes(user_id, status)',
      'CREATE INDEX IF NOT EXISTS idx_codes_type_date ON exchange_codes(exchange_type, issued_datetime)',
      'CREATE INDEX IF NOT EXISTS idx_logs_user_type ON operation_logs(user_id, operation_type)'
    ];
  
    for (const sql of indexes) {
      await env.DB.prepare(sql).run();
    }
  }

  // 批量插入优化
  static async batchInsert(tableName, records, batchSize = 100) {
    const batches = [];
    for (let i = 0; i < records.length; i += batchSize) {
      batches.push(records.slice(i, i + batchSize));
    }
  
    for (const batch of batches) {
      const placeholders = batch.map(() => '(?)').join(',');
      const sql = `INSERT INTO ${tableName} VALUES ${placeholders}`;
      await env.DB.prepare(sql).bind(...batch).run();
    }
  }
}
```

### 2. 缓存机制
```javascript
// KV缓存管理
class CacheManager {
  static async get(key) {
    try {
      const cached = await env.KV.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch {
      return null;
    }
  }

  static async set(key, value, ttl = 3600) {
    try {
      await env.KV.put(key, JSON.stringify(value), { expirationTtl: ttl });
    } catch (error) {
      console.error('Cache set failed:', error);
    }
  }

  static async getUserProfile(userId) {
    const cacheKey = `user_profile_${userId}`;
    let profile = await this.get(cacheKey);
  
    if (!profile) {
      profile = await this.fetchUserProfile(userId);
      await this.set(cacheKey, profile, 1800); // 30分钟缓存
    }
  
    return profile;
  }

  static async invalidateUserCache(userId) {
    const keys = [
      `user_profile_${userId}`,
      `user_stats_${userId}`,
      `user_codes_${userId}`
    ];
  
    for (const key of keys) {
      await env.KV.delete(key);
    }
  }
}
```

### 3. 错误处理和日志
```javascript
// 统一错误处理
class ErrorHandler {
  static async handle(error, request, context = {}) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('cf-connecting-ip'),
      ...context
    };
  
    // 记录错误日志
    await this.logError(errorInfo);
  
    // 返回用户友好的错误信息
    return this.createErrorResponse(error);
  }

  static async logError(errorInfo) {
    try {
      await env.DB.prepare(`
        INSERT INTO operation_logs (operation_type, operation_desc, ip_address, status, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
      `).bind('error', JSON.stringify(errorInfo), errorInfo.ip, 'error').run();
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }

  static createErrorResponse(error) {
    const isProduction = env.ENVIRONMENT === 'production';
  
    return new Response(JSON.stringify({
      success: false,
      error: isProduction ? '系统错误，请稍后重试' : error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
```

## 🔄 数据迁移和备份

### 1. 数据库迁移脚本
```javascript
// 版本迁移管理
class MigrationManager {
  static migrations = {
    '1.0.0': async () => {
      // 初始表创建
      await this.createInitialTables();
    },
  
    '1.1.0': async () => {
      // 添加专有ID字段
      await env.DB.prepare(`
        ALTER TABLE oauth_users ADD COLUMN site_user_id VARCHAR(20)
      `).run();
    
      // 为现有用户生成专有ID
      await this.generateSiteUserIds();
    },
  
    '1.2.0': async () => {
      // 添加管理员会话表
      await env.DB.prepare(`
        CREATE TABLE admin_sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          session_token VARCHAR(128) UNIQUE,
          username VARCHAR(50),
          ip_address VARCHAR(50),
          expires_at DATETIME,
          created_at DATETIME
        )
      `).run();
    }
  };

  static async runMigrations() {
    const currentVersion = await this.getCurrentVersion();
    const targetVersion = '1.2.0';
  
    for (const [version, migration] of Object.entries(this.migrations)) {
      if (this.compareVersions(version, currentVersion) > 0 && 
          this.compareVersions(version, targetVersion) <= 0) {
        console.log(`Running migration ${version}...`);
        await migration();
        await this.setCurrentVersion(version);
      }
    }
  }
}
```

### 2. 自动备份功能
```javascript
// 数据备份管理
class BackupManager {
  static async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupData = {
      timestamp,
      users: await this.exportUsers(),
      signinRecords: await this.exportSigninRecords(),
      exchangeCodes: await this.exportExchangeCodes(),
      configs: await this.exportConfigs()
    };
  
    // 保存到KV存储
    await env.KV.put(`backup_${timestamp}`, JSON.stringify(backupData));
  
    // 清理旧备份（保留最近30个）
    await this.cleanupOldBackups();
  
    return timestamp;
  }

  static async restoreBackup(backupId) {
    const backupData = await env.KV.get(`backup_${backupId}`);
    if (!backupData) {
      throw new Error('备份文件不存在');
    }
  
    const data = JSON.parse(backupData);
  
    // 恢复数据（需要事务支持）
    await this.restoreUsers(data.users);
    await this.restoreSigninRecords(data.signinRecords);
    await this.restoreExchangeCodes(data.exchangeCodes);
    await this.restoreConfigs(data.configs);
  }
}
```

## 🎯 核心特性总结

### ✅ 用户体验
- OAuth2 一键登录
- 主界面显示签到统计（连续/总天数）
- UTC+8 精确签到时间
- 弹窗显示兑换码（防重复）
- 分页浏览历史记录
- 深色模式适配

### ✅ 管理功能
- 用户名密码登录管理后台
- TXT文件批量导入
- 灵活的奖励配置
- 高级用户搜索和批量操作
- 签到排名奖励机制
- **完整的分页系统（5-50条可调）**
- 数据导出和备份功能
- 实时统计看板

### ✅ 技术特点
- 单文件部署
- 自动建表初始化
- 性能优化（索引、缓存）
- 防SQL注入
- JWT会话管理
- 错误处理和日志记录
- 数据库迁移支持

### ✅ 分页优化
- **管理员所有列表支持分页**
- **可选择5,10,15,20,30,50条每页**
- **记住用户分页偏好**
- **高级搜索结果分页**
- **批量操作支持分页选择**
- **数据导出支持筛选结果**

### ✅ 数据安全
- 用户名密码管理员认证
- 会话管理和超时控制
- 操作日志记录
- 数据备份恢复
- 错误监控和告警

---

**🎉 功能完整，企业级系统！**

这是一个功能完整的企业级签到兑换码管理系统，具备：
- 完善的用户界面（显示签到统计）
- 安全的管理员认证系统
- 灵活的分页管理（5-50条可调）
- 高性能的数据处理
- 完整的备份恢复机制

只需要一个 `worker.js` 文件和正确的环境变量配置，即可部署一个功能完整的生产级系统。